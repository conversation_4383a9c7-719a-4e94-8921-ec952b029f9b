# AI Hindi Story Audio Generator

An autonomous Python-based agent that creates Hindi story audio content based on real incidents and fictional stories. Generates complete story data, character reference images, and high-quality narration audio files ready for manual video assembly.

## 🔧 Features

- Fully autonomous story generation pipeline
- Research-driven story generation in Hindi
- Character extraction and reference image generation
- High-quality text-to-speech narration
- Dialogue segmentation for optimal audio pacing
- Audio-only output for manual video assembly

## 🌐 Language Configuration

The system is designed with the following language configuration:

- **Narration Text**: All story narration is generated in Hindi using Devanagari script. This is the content that will be spoken in the final video.
- **Visual Descriptions**: All visual descriptions for image generation are in English.
- **Technical Elements**: All other elements of the system (scene transitions, narrative purpose, file names, logs, etc.) are in English.

This configuration ensures that the final video has authentic Hindi narration while maintaining the technical functionality of the system in English.

## 📑 Project Structure

```
ai-video-story-generator/
├── main.py                 # Main orchestrator script
├── agents/                 # AI agents for different tasks
│   ├── researcher.py       # Gathers information on the topic
│   ├── editor.py           # Enhances research data
│   ├── writer.py           # Writes Hindi story based on real incidents
│   ├── dialogue_splitter.py # Splits story into segments
│   ├── story_editor.py     # Interactive story editing
│   └── character_consistency.py # Character extraction and reference generation
├── models/                 # Pydantic data models
│   ├── __init__.py         # Package initialization
│   └── schema.py           # Data schema definitions
├── utils/                  # Utility modules
│   ├── tts_generator.py    # TTS integration (ElevenLabs and espeak-ng)
│   ├── image_generator.py  # Character reference image generation
│   ├── parsers.py          # Langchain output parsers
│   └── content_moderation.py # Content filtering and moderation
└── assets/                 # Generated assets
    ├── audio/              # Generated audio files
    ├── characters/         # Character reference images
    ├── story.json          # Structured story data
    ├── character_consistency.json # Character data and references
    └── dialogue_segments.json # Segmented dialogue data
```

## 🚀 Getting Started

### Prerequisites

- Python 3.8+
- API keys for:
  - OpenAI (for LLM models like GPT-4o-mini) - Optional if using other providers
  - OpenRouter (for accessing multiple LLM models) - Optional if using other providers
  - Replicate (for open-source LLMs and image generation) - Optional if using other providers
  - Serper (Google search API)
  - ElevenLabs (TTS) - Optional when using espeak-ng TTS in dev mode
  - DeepInfra (FLUX-1-dev) - Optional if using other image providers
  - Black Forest Labs (BFL) - Optional if using other image providers
- espeak-ng package (installed via system package manager) for local development mode

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/ai-video-story-generator.git
   cd ai-video-story-generator
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Create a `.env` file with your API keys:
   ```
   # Required for all configurations
   SERPER_API_KEY=your_serper_api_key

   # LLM Provider API Keys (choose one or more)
   OPENAI_API_KEY=your_openai_api_key
   OPENROUTER_API_KEY=your_openrouter_api_key
   REPLICATE_API_KEY=your_replicate_api_key

   # TTS API Key (optional if using --dev mode)
   ELEVENLABS_API_KEY=your_elevenlabs_api_key

   # Image Generation API Keys (choose one)
   DEEPINFRA_API_KEY=your_deepinfra_api_key
   BFL_API_KEY=your_bfl_api_key
   # Note: REPLICATE_API_KEY can also be used for image generation
   ```

   Note: Other configuration options that were previously in the `.env` file have been moved to command-line arguments.

   You'll need to sign up for the following services to get the API keys:
   - [OpenAI](https://platform.openai.com/signup) - For LLM models (GPT-4o-mini, etc.)
   - [OpenRouter](https://openrouter.ai/) - For accessing multiple LLM models (Claude, Llama, etc.)
   - [Replicate](https://replicate.com/) - For open-source models and image generation
   - [Serper](https://serper.dev/signup) - For Google search API
   - [ElevenLabs](https://elevenlabs.io/sign-up) - For text-to-speech
   - [DeepInfra](https://deepinfra.com/signup) - For FLUX-1-dev image generation
   - [Black Forest Labs](https://api.bfl.ai/) - For direct FLUX API access

### Usage

Run the script with a story title and type:

```bash
# For a story based on real incidents
python main.py --title "मुंबई की बारिश" --story-type real --context "2023 में मुंबई में आई भीषण बाढ़"

# For a fictional story (context is required)
python main.py --title "जादुई जंगल" --story-type fictional --context "एक ऐसा जंगल जहां जानवर बात करते हैं"
```

The script will:
1. Research the topic (for real incidents) or create fictional elements (for fictional stories)
2. Generate a Hindi story based on the selected story type
3. Extract characters and generate reference images
4. Split dialogue into optimal segments for narration
5. Create high-quality narration audio files
6. Save all data in organized JSON format for manual video assembly

## 🔖 Configuration

You can customize the behavior by passing command-line arguments:

### Required Arguments

- `--title`: Title of the story (required)
- `--context`: Additional context for the story (required for fictional stories)

### Story Configuration

- `--story-type`: Type of story (`real`, `fictional`, or `mixed`). Default: `real`
- `--genre`: Genre of the story (`thriller`, `romance`, `mystery`, `action`, `drama`, `comedy`, `sci-fi`, `fantasy`). Default: `thriller`
- `--references`: URLs to articles, blogs, or reports to analyze for real or mixed stories

### Image Generation Configuration

- `--image-style`: Visual aesthetic style for generated images (`realistic`, `anime`, `cartoon`, `watercolor`, `oil-painting`, `sketch`, `noir`, `vintage`). Default: `realistic`
- `--deepinfra-model`: Model to use for image generation. Default: `black-forest-labs/FLUX-1.1-pro`
- `--use-parallel-image-generation`: Enable parallel processing for image generation (flag). Default: `True`
- `--no-parallel-image-generation`: Disable parallel processing for image generation (flag)
- `--max-concurrent-requests`: Maximum number of concurrent requests for parallel image generation. Default: `200`

### Audio Generation Configuration

- `--elevenlabs-voice-id`: ElevenLabs voice ID to use for narration. Default: `MaBqnF6LpI8cAT5sGihk`
- `--dev`: Use espeak-ng TTS for local development (no API costs). Default: `True`
- `--no-dev`: Use ElevenLabs TTS for production quality (requires API key). Default: `False`

### LLM Model Configuration

- `--model`: LLM model to use for all agents. Default: `gpt-4o-mini`
- `--provider`: LLM provider to use (`openai`, `openrouter`, or `replicate`). Default: `openai`
- `--max-tokens-per-minute`: Maximum tokens per minute for OpenAI API rate limiting. Default: `30000`

### Agent-Specific LLM Configuration

You can configure different models and providers for individual agents. If not specified, agents will use the global `--model` and `--provider` settings.

#### ResearcherAgent Configuration
- `--researcher-model`: LLM model for ResearcherAgent (defaults to `--model`)
- `--researcher-provider`: LLM provider for ResearcherAgent (defaults to `--provider`)

#### EditorAgent Configuration
- `--editor-model`: LLM model for EditorAgent (defaults to `--model`)
- `--editor-provider`: LLM provider for EditorAgent (defaults to `--provider`)

#### WriterAgent Configuration
- `--writer-model`: LLM model for WriterAgent (defaults to `--model`)
- `--writer-provider`: LLM provider for WriterAgent (defaults to `--provider`)

#### DialogueSplitterAgent Configuration
- `--dialogue-splitter-model`: LLM model for DialogueSplitterAgent (defaults to `--model`)
- `--dialogue-splitter-provider`: LLM provider for DialogueSplitterAgent (defaults to `--provider`)

#### ImageDescriptionGenerator Configuration
- `--image-desc-gen-model`: LLM model for ImageDescriptionGenerator (defaults to `--model`)
- `--image-desc-gen-provider`: LLM provider for ImageDescriptionGenerator (defaults to `--provider`)

#### StoryEditorAgent Configuration
- `--story-editor-model`: LLM model for StoryEditorAgent (defaults to `--model`)
- `--story-editor-provider`: LLM provider for StoryEditorAgent (defaults to `--provider`)

### Provider-Specific Information

#### OpenAI Provider
- Supports all GPT models (GPT-4, GPT-3.5, etc.)
- Requires `OPENAI_API_KEY` in `.env` file
- Includes built-in rate limiting

#### OpenRouter Provider
- Access to multiple models: Claude, Llama, Mistral, etc.
- Requires `OPENROUTER_API_KEY` in `.env` file
- Cost-effective alternative to OpenAI

#### Replicate Provider
- Access to open-source models: Llama, Mistral, CodeLlama, etc.
- Requires `REPLICATE_API_KEY` in `.env` file
- Supports both text generation and image generation
- Good for experimenting with different model architectures

### Image Generation Configuration

- `--image-provider`: Choose between `deepinfra`, `bfl`, or `replicate`
- `--image-model`: Specify the image generation model
- `--image-style`: Visual aesthetic style for generated images

**Supported Image Providers:**
- `deepinfra`: FLUX models via DeepInfra API
- `bfl`: Black Forest Labs direct API
- `replicate`: FLUX, SDXL, and other models via Replicate

### Debug and Logging Configuration

- `--verbose`: Enable verbose output from CrewAI agents. Default: `False`
- `--interactive-editing`: Enable interactive story editing workflow. Default: `True`

### Video Output Configuration

- `--create-fast-version`: Create an additional 2x speed video output for quick review. Default: `False`

### Example with Advanced Options

```bash
python main.py --title "रहस्यमयी महल" \
  --story-type mixed \
  --genre mystery \
  --context "एक पुराना महल जिसमें अजीब घटनाएँ होती हैं" \
  --references "https://example.com/article1" "https://example.com/article2" \
  --image-style watercolor \
  --deepinfra-model "black-forest-labs/FLUX-1.1-pro" \
  --no-dev \
  --elevenlabs-voice-id "MaBqnF6LpI8cAT5sGihk" \
  --model "gpt-4o" \
  --provider "openai" \
  --max-tokens-per-minute 30000 \
  --max-concurrent-requests 100 \
  --verbose \
  --create-fast-version
```

### Example with Agent-Specific Configuration

```bash
python main.py --title "भविष्य की कहानी" \
  --story-type fictional \
  --genre sci-fi \
  --context "2050 में भारत में रोबोट और इंसानों की दोस्ती" \
  --model "gpt-4o-mini" \
  --provider "openai" \
  --researcher-model "gpt-4o" \
  --researcher-provider "openrouter" \
  --writer-model "claude-3-sonnet" \
  --writer-provider "openrouter" \
  --image-desc-gen-model "gpt-4o" \
  --dialogue-splitter-provider "openrouter" \
  --verbose
```

This example shows:
- Global defaults: `gpt-4o-mini` model with `openai` provider
- ResearcherAgent: Uses `gpt-4o` model with `openrouter` provider
- WriterAgent: Uses `claude-3-sonnet` model with `openrouter` provider
- ImageDescriptionGenerator: Uses `gpt-4o` model with default `openai` provider
- DialogueSplitterAgent: Uses default `gpt-4o-mini` model with `openrouter` provider
- EditorAgent and StoryEditorAgent: Use global defaults

### Example with Replicate Provider

```bash
python main.py --title "AI की दुनिया" \
  --story-type fictional \
  --genre sci-fi \
  --context "भविष्य में AI और इंसानों का सहयोग" \
  --provider "replicate" \
  --model "meta/llama-2-70b-chat" \
  --image-provider "replicate" \
  --image-model "black-forest-labs/flux-schnell" \
  --writer-model "mistralai/mixtral-8x7b-instruct-v0.1" \
  --verbose
```

This example shows:
- Using Replicate for both text and image generation
- Global LLM: Llama 2 70B Chat via Replicate
- Image generation: FLUX Schnell via Replicate
- WriterAgent: Mixtral 8x7B via Replicate
- Other agents: Use global Llama 2 70B Chat

For local development without API costs:

```bash
python main.py --title "जादुई जंगल" \
  --story-type fictional \
  --genre fantasy \
  --context "एक ऐसा जंगल जहां जानवर बात करते हैं" \
  --image-style cartoon \
  --dev \
  --model "gpt-4o-mini" \
  --interactive-editing
```

API keys should still be configured in the `.env` file:

## 🧩 Data Models and Parsers

The project uses Pydantic models and Langchain parsers to ensure consistent data formatting throughout the pipeline:

### Pydantic Models

- `ResearchData`: Structured research information for story creation
- `Story`: Complete story with title and scenes
- `Scene`: Individual scene with narration and visual description
- `SceneSegment`: Segment of a scene with narration and visual cue
- `ImagePrompt`: Image generation prompt for a scene segment

### Langchain Parsers

The project uses Langchain's `PydanticOutputParser` to ensure that LLM outputs conform to the expected data structures. This helps prevent formatting issues and ensures all scenes are properly processed.

## 📝 Notes

- The first run may take some time as it generates all assets from scratch
- API usage costs apply for OpenAI, ElevenLabs, Serper, and DeepInfra
- The quality of the final video depends on the quality of the generated assets
- Structured data models ensure consistent processing of all scenes

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgements

- [CrewAI](https://github.com/joaomdmoura/crewai) for agent orchestration
- [ElevenLabs](https://elevenlabs.io/) for production-quality text-to-speech
- [espeak-ng](https://github.com/espeak-ng/espeak-ng) for local development text-to-speech
- [FLUX-1-dev](https://deepinfra.com/) for image generation
- [MoviePy](https://zulko.github.io/moviepy/) for video assembly
