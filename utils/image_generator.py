"""
Image Generator
-------------
Generates images using a Image Generation model via deepinfra.com API.
Supports parallel image generation with rate limiting.
"""

import os
import time
import base64
import logging
import traceback
from typing import Optional

import requests

from utils.content_moderation import sanitize_prompt
from utils.replicate_image_generator import ReplicateImageGenerator

logger = logging.getLogger(__name__)


class ImageGenerator:
    def __init__(self,
                 model: str = "black-forest-labs/FLUX-1-schnell",
                 provider: str = "deepinfra",
                 max_concurrent_requests: int = 200,
                 use_parallel: bool = True):
        """
        Initialize the image generator with necessary API keys and configuration.

        Args:
            model (str): Image generation model to use.
            provider (str): Image generation provider (e.g., "deepinfra", "bfl", "replicate").
            max_concurrent_requests (int): Maximum number of concurrent requests. Defaults to 200.
            use_parallel (bool): Whether to use parallel processing. Defaults to True.
        """
        self.provider = provider
        self.model = model
        self.max_concurrent_requests = max_concurrent_requests
        self.use_parallel = use_parallel

        if self.provider == "deepinfra":
            self.api_key = os.getenv("DEEPINFRA_API_KEY")

            if not self.api_key:
                raise ValueError("DEEPINFRA_API_KEY environment variable not set.")

            self.base_url = "https://api.deepinfra.com/v1/openai/images/generations"

        elif self.provider == "bfl":
            self.api_key = os.getenv("BFL_API_KEY")

            if not self.api_key:
                raise ValueError("BFL_API_KEY environment variable not set.")

            self.base_url = f"https://api.bfl.ai/v1/{self.model}"

        elif self.provider == "replicate":
            self.api_key = os.getenv("REPLICATE_API_KEY")

            if not self.api_key:
                raise ValueError("REPLICATE_API_KEY environment variable not set.")

            # Initialize Replicate image generator
            self.replicate_generator = ReplicateImageGenerator(model=model, api_token=self.api_key)

        else:
            raise ValueError(f"Unsupported image provider: {self.provider}")

        self._semaphore = None
        self.max_retries = 10
        self.retry_delay = 3  # seconds

    def _generate_image_from_prompt(self,
                                    prompt: str,
                                    reference_image_path: Optional[str] = None,
                                    width: int = 1024,
                                    height: int = 1024,
                                    image_style: str = 'realistic') -> Optional[bytes]:
        """
        Generate an image using the model (synchronous version).

        Args:
            prompt (str): The image prompt
            reference_image_path (str, optional): Path to a reference image for image-to-image generation
            width (int, optional): Width of the generated image. Defaults to 1024.
            height (int, optional): Height of the generated image. Defaults to 1024.
            image_style (str, optional): Visual aesthetic style for the image. Defaults to 'realistic'.

        Returns:
            Optional[bytes]: The generated image data or None if generation fails
        """

        # Apply content moderation to the prompt first
        sanitized_prompt = sanitize_prompt(prompt)

        # Add style guidance to the prompt if not already included
        if image_style.lower() not in sanitized_prompt.lower():
            styled_prompt = f"{sanitized_prompt}, {image_style} style"
        else:
            styled_prompt = sanitized_prompt

        payload = {}

        if self.provider == "deepinfra":
            size = f"{width}x{height}"

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "prompt": styled_prompt,
                "model": self.model,
                "n": 1,
                "size": size,
                "response_format": "b64_json"
            }

            if reference_image_path:
                logger.warning("DeepInfra (OpenAI-compatible) API does not directly support image-to-image. Using text-to-image with prompt enhancement.")
                payload["prompt"] = f"{styled_prompt} (consistent with previous image, {image_style} style)"

        elif self.provider == "bfl":
            headers = {
                "X-Key": f"{self.api_key}",
                "Content-Type": "application/json"
            }

            # BFL specific payload structure with flux-kontext-pro support
            payload = {
                "prompt": styled_prompt,
                "safety_tolerance": 6,
                "output_format": "jpeg",
                "aspect_ratio": "16:9",
                "prompt_upsampling": False,
            }

            # Handle reference image for flux-kontext-pro model
            if reference_image_path:
                try:
                    with open(reference_image_path, "rb") as f:
                        encoded_image = base64.b64encode(f.read()).decode('utf-8')

                    # Use appropriate field name based on model
                    if "flux-kontext-pro" in self.model.lower():
                        payload["input_image"] = encoded_image
                        logger.info(f"Using reference image for flux-kontext-pro: {reference_image_path}")
                    else:
                        payload["input_image"] = encoded_image
                        logger.info(f"Using reference image for {self.model}: {reference_image_path}")

                except FileNotFoundError:
                    logger.error(f"Reference image not found: {reference_image_path}. Proceeding without it.")

                except Exception as e:
                    logger.error(f"Error encoding reference image {reference_image_path}: {e}. Proceeding without it.")

        elif self.provider == "replicate":
            # Use Replicate image generator
            try:
                if reference_image_path:
                    image_data = self.replicate_generator.generate_image_with_reference(
                        prompt=styled_prompt,
                        reference_image_path=reference_image_path,
                        width=width,
                        height=height
                    )
                else:
                    image_data = self.replicate_generator.generate_image(
                        prompt=styled_prompt,
                        width=width,
                        height=height
                    )
                return image_data
            except Exception as e:
                logger.error(f"Error generating image with Replicate: {str(e)}")
                return None

        else:
            raise ValueError(f"Unsupported image provider: {self.provider}")

        try:
            # Send the request and handle the response
            response = requests.post(self.base_url, headers=headers, json=payload)
            response.raise_for_status()
            result = response.json()

            print(payload["prompt"])
            print(response.json())
            print()

            if self.provider == "deepinfra":
                if "data" in result and len(result["data"]) > 0 and "b64_json" in result["data"][0]:
                    # DeepInfra response is valid, decode and return
                    image_data = base64.b64decode(result["data"][0]["b64_json"])
                    return image_data

                else:
                    # Unexpected DeepInfra API response
                    logger.error(f"Unexpected DeepInfra API response: {result}")
                    return None

            elif self.provider == "bfl":
                # BFL response contains a polling URL
                polling_url = result["polling_url"]

                # Initial delay before polling
                time.sleep(self.retry_delay)

                for _ in range(self.max_retries):
                    # Poll the polling URL
                    polling_response = requests.get(polling_url)
                    polling_response.raise_for_status()

                    result = polling_response.json()["result"]

                    if result and "sample" in result:
                        # Image is ready, download it
                        image_response = requests.get(result["sample"])

                        # Check if the request was successful
                        if not image_response.ok:
                            logger.error(f"Failed to download image: {image_response.text}")
                            return None

                        return image_response.content

                    # Image is not ready, wait and retry
                    print(polling_response.json())
                    logger.warning(f"Image not ready, retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)

                # Image not ready after retries, return None
                logger.error(f"Image not ready after {self.max_retries} retries.")
                return None

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Error generating image with {self.provider}: {str(e)}")
            return None










