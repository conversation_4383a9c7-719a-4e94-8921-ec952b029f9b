"""
Replicate Image Generator
------------------------
Generates images using Replicate's image generation models like FLUX, SDXL, etc.
"""

import os
import logging
import base64
import requests
from typing import Optional, Dict, Any
import replicate

logger = logging.getLogger(__name__)


class ReplicateImageGenerator:
    """
    Image generator that uses Replicate API for image generation.
    
    Supports various models available on Replicate including FLUX, SDXL, and others.
    """
    
    def __init__(self, 
                 model: str = "black-forest-labs/flux-schnell",
                 api_token: Optional[str] = None):
        """
        Initialize the Replicate image generator.
        
        Args:
            model (str): The Replicate model to use. Default: "black-forest-labs/flux-schnell"
            api_token (str, optional): The Replicate API token. If not provided, will use REPLICATE_API_KEY env var.
        """
        self.model = model
        
        # Set up API token
        self.api_token = api_token or os.getenv('REPLICATE_API_KEY')
        if not self.api_token:
            raise ValueError("Replicate API token is required. Set it via the api_token parameter or REPLICATE_API_KEY environment variable.")
        
        # Configure replicate client
        os.environ['REPLICATE_API_TOKEN'] = self.api_token
        
        logger.info(f"Initialized ReplicateImageGenerator with model {model}")
    
    def generate_image(self, 
                      prompt: str,
                      width: int = 1024,
                      height: int = 1024,
                      num_inference_steps: int = 4,
                      guidance_scale: float = 0.0,
                      seed: Optional[int] = None,
                      **kwargs) -> bytes:
        """
        Generate a single image using the Replicate API.
        
        Args:
            prompt (str): The text prompt for image generation
            width (int): Image width. Default: 1024
            height (int): Image height. Default: 1024
            num_inference_steps (int): Number of inference steps. Default: 4
            guidance_scale (float): Guidance scale. Default: 0.0
            seed (int, optional): Random seed for reproducibility
            **kwargs: Additional model-specific parameters
            
        Returns:
            bytes: The generated image as bytes
        """
        # Prepare input parameters based on the model
        input_params = self._prepare_input_params(
            prompt=prompt,
            width=width,
            height=height,
            num_inference_steps=num_inference_steps,
            guidance_scale=guidance_scale,
            seed=seed,
            **kwargs
        )
        
        try:
            logger.debug(f"Generating image with Replicate model {self.model}")
            logger.debug(f"Prompt: {prompt[:100]}...")
            
            # Call Replicate API
            output = replicate.run(self.model, input=input_params)
            
            # Get the image URL from the output
            image_url = output.url

            # Download the image
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()
            
            return response.content
            
        except Exception as e:
            logger.error(f"Error generating image with Replicate: {str(e)}")
            raise e
    
    def _prepare_input_params(self, 
                             prompt: str,
                             width: int,
                             height: int,
                             num_inference_steps: int,
                             guidance_scale: float,
                             seed: Optional[int],
                             **kwargs) -> Dict[str, Any]:
        """
        Prepare input parameters for the specific Replicate model.
        
        Args:
            prompt (str): The text prompt
            width (int): Image width
            height (int): Image height
            num_inference_steps (int): Number of inference steps
            guidance_scale (float): Guidance scale
            seed (int, optional): Random seed
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Formatted input parameters
        """
        # Base parameters that work with most models
        params = {
            "prompt": prompt,
            "width": width,
            "height": height,
        }
        
        # Model-specific parameter handling
        if "flux" in self.model.lower():
            # FLUX models typically use these parameters
            params.update({
                "num_inference_steps": num_inference_steps,
                "guidance_scale": guidance_scale,
            })
            if seed is not None:
                params["seed"] = seed
                
        elif "sdxl" in self.model.lower():
            # SDXL models typically use these parameters
            params.update({
                "num_inference_steps": max(num_inference_steps, 20),  # SDXL usually needs more steps
                "guidance_scale": max(guidance_scale, 7.5),  # SDXL usually needs higher guidance
            })
            if seed is not None:
                params["seed"] = seed
                
        else:
            # Generic parameters for other models
            if num_inference_steps > 0:
                params["num_inference_steps"] = num_inference_steps
            if guidance_scale > 0:
                params["guidance_scale"] = guidance_scale
            if seed is not None:
                params["seed"] = seed
        
        # Add any additional kwargs
        params.update(kwargs)
        
        return params
    
    def generate_image_with_reference(self,
                                      prompt: str,
                                      reference_image_path: str,
                                      width: int = 1024,
                                      height: int = 1024,
                                      **kwargs) -> bytes:
        """
        Generate an image with a reference image (if the model supports it).
        
        Args:
            prompt (str): The text prompt for image generation
            reference_image_path (str): Path to the reference image
            width (int): Image width. Default: 1024
            height (int): Image height. Default: 1024
            **kwargs: Additional model-specific parameters
            
        Returns:
            bytes: The generated image as bytes
        """
        # Check if the model supports image-to-image
        if "flux-kontext-pro" in self.model.lower():
            # FLUX Canny model supports reference images
            with open(reference_image_path, 'rb') as f:
                reference_image_data = f.read()

            kwargs['image'] = reference_image_data

        else:
            # For models that don't support reference images, enhance the prompt
            logger.warning(f"Model {self.model} may not support reference images. Enhancing prompt instead.")
            prompt = f"{prompt} (consistent with reference style)"

        return self.generate_image(
            prompt=prompt,
            width=width,
            height=height,
            **kwargs
        )
    
    def get_supported_models(self) -> list:
        """
        Get a list of popular image generation models available on Replicate.
        
        Returns:
            list: List of model names
        """
        return [
            "black-forest-labs/flux-schnell",
            "black-forest-labs/flux-dev",
            "black-forest-labs/flux-pro",
            "stability-ai/sdxl",
            "stability-ai/stable-diffusion-xl-base-1.0",
            "lucataco/realistic-vision-v5.1",
            "playgroundai/playground-v2.5-1024px-aesthetic",
        ]
